import { IsNotEmpty, IsString, Length, IsEmail } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class VerifyEmailDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'User email address',
  })
  @IsString()
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({
    example: 'A1B2C3',
    description: 'Verification code',
  })
  @IsString()
  @IsNotEmpty()
  @Length(5)
  code: string;
}
