{"name": "nestjs-smartairport", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "bun run nest build", "format": "bun run prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "bun run nest start", "start:dev": "bun run nest start --watch", "start:debug": "bun run nest start --debug --watch", "start:prod": "bun dist/main.js", "lint": "bun run eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "bun run jest", "test:watch": "bun run jest --watch", "test:cov": "bun run jest --coverage", "test:debug": "bun run --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "bun run jest --config ./test/jest-e2e.json"}, "dependencies": {"@fastify/helmet": "^13.0.1", "@fastify/rate-limit": "^10.3.0", "@fastify/static": "^8.2.0", "@nestjs/axios": "^4.0.0", "@nestjs/cache-manager": "^3.0.1", "@nestjs/class-validator": "^0.13.4", "@nestjs/common": "^11.1.2", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.2", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/mongoose": "^11.0.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.2", "@nestjs/platform-fastify": "^11.1.2", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "^11.2.0", "@nestjs/throttler": "^6.4.0", "@opentelemetry/instrumentation-fastify": "^0.44.2", "@types/date-fns": "^2.6.3", "@types/mongoose": "^5.11.97", "@types/qrcode": "^1.5.5", "axios": "^1.9.0", "bcrypt": "^5.1.1", "cache-manager": "^6.4.3", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "country-list": "^2.3.0", "date-fns": "^4.1.0", "express": "^5.1.0", "fastify": "^5.3.3", "firebase-admin": "^13.4.0", "ioredis": "^5.6.1", "joi": "^17.13.3", "libphonenumber-js": "^1.12.8", "lodash": "^4.17.21", "mongoose": "^8.15.1", "nestjs-circuit-breaker": "^1.0.1", "nestjs-i18n": "^10.5.1", "nodemailer": "^6.10.1", "opentelemetry-instrumentation-express": "^0.41.0", "passport-jwt": "^4.0.1", "pino-pretty": "^13.0.0", "qrcode": "^1.5.4", "redlock": "^5.0.0-beta.2", "reflect-metadata": "^0.2.2", "retry-axios": "^3.1.3", "rxjs": "^7.8.2", "stripe": "^17.7.0", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.27.0", "@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.2", "@swc/cli": "^0.6.0", "@swc/core": "^1.11.29", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.2", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.17", "@types/node": "^22.15.21", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^4.0.1", "@types/pino": "^7.0.5", "@types/supertest": "^6.0.3", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "jest": "^29.7.0", "prettier": "^3.5.3", "source-map-support": "^0.5.21", "supertest": "^7.1.1", "ts-jest": "^29.3.4", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}